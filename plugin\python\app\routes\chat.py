from flask import Blueprint, request, jsonify
from g4f.client import Client
from g4f.Provider import <PERSON><PERSON><PERSON>, Gemini, CopilotAccount
from g4f.cookies import read_cookie_files
read_cookie_files()

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        messages = data.get("messages")

        if not messages:
            return jsonify({"error": "Messages are required"}), 400

        client = Client(provider=CopilotAccount)
        response = client.chat.completions.create(
        messages=messages,    
        )
        return response.choices[0].message.content
    except Exception as e:
        return jsonify({"error": str(e)}), 500
