from __future__ import annotations

import os
import json
import asyncio
import base64
from typing import AsyncIterator
from urllib.parse import quote

try:
    from curl_cffi.requests import AsyncSession
    from curl_cffi import CurlWsFlag, CurlMime
    has_curl_cffi = True
except ImportError:
    has_curl_cffi = False

from g4f.Provider.base_provider import AsyncAuthedProvider, ProviderModelMixin
from g4f.Provider.openai.har_file import get_headers, get_har_files
from g4f.typing import AsyncResult, Messages, MediaListType
from g4f.errors import MissingRequirementsError, NoValidHarFileError, MissingAuthError
from g4f.providers.response import *
from g4f.tools.media import merge_media
from g4f.requests import get_nodriver
from g4f.image import to_bytes, is_accepted_format
from g4f.Provider.helper import get_last_user_message
from g4f.files import get_bucket_dir
from g4f.tools.files import read_bucket
from pathlib import Path
from g4f import debug

# Import the original Copilot class to inherit most functionality
from g4f.Provider.Copilot import Copilot as OriginalCopilot, readHAR, get_access_token_and_cookies, Conversation

class CustomCopilot(OriginalCopilot):
    """
    Custom Copilot provider with configurable session parameters
    to handle HTTP/2 protocol errors and connection issues.
    """
    
    @classmethod
    async def create(
        cls,
        model: str,
        messages: Messages,
        proxy: str = None,
        timeout: int = 30,
        prompt: str = None,
        media: MediaListType = None,
        conversation: BaseConversation = None,
        return_conversation: bool = True,
        useridentitytype: str = "google",
        api_key: str = None,
        # Custom session parameters
        http_version: str = "1.1",  # Force HTTP/1.1 instead of HTTP/2
        verify_ssl: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        custom_headers: dict = None,
        **kwargs
    ) -> AsyncResult:
        if not has_curl_cffi:
            raise MissingRequirementsError('Install or update "curl_cffi" package | pip install -U curl_cffi')
        
        model = cls.get_model(model)
        websocket_url = cls.websocket_url
        headers = None
        
        if cls._access_token or cls.needs_auth:
            if api_key is not None:
                cls._access_token = api_key
            if cls._access_token is None:
                try:
                    cls._access_token, cls._cookies = readHAR(cls.url)
                except NoValidHarFileError as h:
                    debug.log(f"Copilot: {h}")
                    if has_nodriver:
                        yield RequestLogin(cls.label, os.environ.get("G4F_LOGIN_URL", ""))
                        cls._access_token, cls._cookies = await get_access_token_and_cookies(cls.url, proxy)
                    else:
                        raise h
            websocket_url = f"{websocket_url}&accessToken={quote(cls._access_token)}&X-UserIdentityType={quote(useridentitytype)}"
            headers = {"authorization": f"Bearer {cls._access_token}"}

        # Prepare custom headers
        session_headers = headers or {}
        if custom_headers:
            session_headers.update(custom_headers)
        
        # Custom session configuration to handle HTTP/2 issues
        session_config = {
            "timeout": timeout,
            "proxy": proxy,
            "impersonate": "chrome",
            "headers": session_headers,
            "cookies": cls._cookies,
            "verify": verify_ssl,
        }
        
        # Force HTTP/1.1 if specified
        if http_version == "1.1":
            session_config["http_version"] = "1.1"
        
        # Retry logic for handling connection issues
        for attempt in range(max_retries):
            try:
                async with AsyncSession(**session_config) as session:
                    if cls._access_token is not None:
                        cls._cookies = session.cookies.jar if hasattr(session.cookies, "jar") else session.cookies
                        
                        # Custom headers for the problematic request
                        user_request_headers = {"x-useridentitytype": useridentitytype}
                        if custom_headers:
                            user_request_headers.update(custom_headers)
                        
                        response = await session.get(
                            "https://copilot.microsoft.com/c/api/user?api-version=2", 
                            headers=user_request_headers
                        )
                        
                        if response.status_code == 401:
                            raise MissingAuthError("Status 401: Invalid access token")
                        response.raise_for_status()
                        user = response.json().get('firstName')
                        if user is None:
                            if cls.needs_auth:
                                raise MissingAuthError("No user found, please login first")
                            cls._access_token = None
                        else:
                            debug.log(f"Copilot: User: {user}")
                    
                    # Continue with the rest of the original logic...
                    # (This would include the conversation creation and websocket handling)
                    # For now, we'll delegate to the parent class for the remaining logic
                    break
                    
            except Exception as e:
                debug.log(f"CustomCopilot: Attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    raise e
        
        # Delegate the rest to the original implementation
        # Note: This is a simplified version. In a full implementation,
        # you'd need to copy the entire websocket handling logic here
        async for chunk in super().create(model, messages, proxy=proxy, timeout=timeout, 
                                         prompt=prompt, media=media, conversation=conversation,
                                         return_conversation=return_conversation, 
                                         useridentitytype=useridentitytype, api_key=api_key, **kwargs):
            yield chunk
