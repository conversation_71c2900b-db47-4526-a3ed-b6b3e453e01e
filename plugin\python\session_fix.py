"""
Session configuration fixes for g4f Copilot HTTP/2 protocol errors.
This module provides utilities to modify session parameters for better compatibility.
"""

import asyncio
from typing import Dict, Any
from curl_cffi.requests import AsyncSession

class SessionConfig:
    """Configuration class for customizing AsyncSession parameters."""
    
    def __init__(
        self,
        http_version: str = "1.1",
        verify_ssl: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        custom_headers: Dict[str, str] = None,
        timeout: int = 60,
        impersonate: str = "chrome"
    ):
        self.http_version = http_version
        self.verify_ssl = verify_ssl
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.custom_headers = custom_headers or {}
        self.timeout = timeout
        self.impersonate = impersonate
    
    def get_session_kwargs(self, **additional_kwargs) -> Dict[str, Any]:
        """Get session configuration as kwargs for AsyncSession."""
        config = {
            "timeout": self.timeout,
            "impersonate": self.impersonate,
            "verify": self.verify_ssl,
        }
        
        # Force HTTP/1.1 if specified
        if self.http_version == "1.1":
            config["http_version"] = "1.1"
        
        # Merge with additional kwargs
        config.update(additional_kwargs)
        return config

async def robust_get_request(
    session: AsyncSession,
    url: str,
    headers: Dict[str, str] = None,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    **kwargs
):
    """
    Make a GET request with retry logic and error handling.
    
    Args:
        session: AsyncSession instance
        url: URL to request
        headers: Request headers
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries (with exponential backoff)
        **kwargs: Additional arguments for the request
    
    Returns:
        Response object
    
    Raises:
        Exception: If all retry attempts fail
    """
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            response = await session.get(url, headers=headers, **kwargs)
            return response
        except Exception as e:
            last_exception = e
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            
            if attempt < max_retries - 1:
                delay = retry_delay * (2 ** attempt)  # Exponential backoff
                print(f"Retrying in {delay} seconds...")
                await asyncio.sleep(delay)
            else:
                print(f"All {max_retries} attempts failed")
    
    raise last_exception

# Predefined configurations for common issues
HTTP1_CONFIG = SessionConfig(
    http_version="1.1",
    custom_headers={
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Connection": "keep-alive"
    },
    max_retries=3,
    retry_delay=2.0,
    timeout=60
)

CONSERVATIVE_CONFIG = SessionConfig(
    http_version="1.1",
    verify_ssl=True,
    custom_headers={
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "close"  # Don't keep connections alive
    },
    max_retries=5,
    retry_delay=3.0,
    timeout=30
)

def apply_session_fix():
    """
    Apply session fixes by monkey-patching the original Copilot provider.
    This is a more aggressive approach that modifies the existing provider.
    """
    try:
        from g4f.Provider import Copilot
        original_create = Copilot.create
        
        @classmethod
        async def patched_create(cls, *args, **kwargs):
            print("🔧 Applying session fixes for HTTP/2 protocol errors...")
            
            # Add custom session configuration
            kwargs.setdefault('timeout', 60)
            
            # You could modify the session creation here
            # This is a placeholder for more advanced patching
            async for chunk in original_create(*args, **kwargs):
                yield chunk
        
        Copilot.create = patched_create
        print("✅ Session fixes applied successfully")
        
    except ImportError as e:
        print(f"❌ Could not apply session fixes: {e}")

if __name__ == "__main__":
    print("Session configuration utilities loaded.")
    print("Available configurations:")
    print("- HTTP1_CONFIG: Forces HTTP/1.1 with standard headers")
    print("- CONSERVATIVE_CONFIG: Most conservative settings for problematic connections")
